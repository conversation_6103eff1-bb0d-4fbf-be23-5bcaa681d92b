import { MailIcon, PlusCircleIcon, type LucideIcon } from 'lucide-react';

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/Components/ui/sidebar';
import React from 'react';
import { Link } from '@inertiajs/react';

export function NavMain({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon?: LucideIcon;
  }[];
}) {
  return (
    <SidebarGroup>
      <SidebarGroupContent className="flex flex-col gap-2">
        <SidebarMenu>
          <SidebarMenuItem className="flex items-center gap-2">
            {/*<SidebarMenuButton*/}
            {/*  tooltip="Quick Create"*/}
            {/*  className="min-w-8 bg-primary text-primary-foreground duration-200 ease-linear hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground"*/}
            {/*>*/}
            {/*  <PlusCircleIcon />*/}
            {/*  <span>Quick Create</span>*/}
            {/*</SidebarMenuButton>*/}
            {/*<Button*/}
            {/*  size="icon"*/}
            {/*  className="h-9 w-9 shrink-0 group-data-[collapsible=icon]:opacity-0"*/}
            {/*  variant="outline"*/}
            {/*>*/}
            {/*  <MailIcon />*/}
            {/*  <span className="sr-only">Inbox</span>*/}
            {/*</Button>*/}
          </SidebarMenuItem>
        </SidebarMenu>
        <SidebarMenu>
          {items.map(item => (
            <Link href={item.url}>
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton tooltip={item.title}>
                  {item.icon && <item.icon />}
                  <span>{item.title}</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </Link>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
